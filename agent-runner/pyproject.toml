[project]
name = "agent-runner"
version = "0.1.0"
description = "FastAPI LangGraph Agent with SSE support"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.112.0",
    "uvicorn[standard]>=0.30.0",
    "langgraph>=0.2.0",
    "langchain-core>=0.3.0",
    "langchain-openai>=0.2.0",
    "langchain-community>=0.3.0",
    "pydantic>=2.9.0",
    "python-dotenv>=1.0.0",
    "sse-starlette>=2.1.0",
    "aiofiles>=24.1.0",
    "mcp>=1.0.0",
    "httpx>=0.27.0",
    "langchain-mcp-adapters>=0.1.9",
    "jinja2>=3.1.6",
    "json-repair>=0.48.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[project.scripts]
agent-runner = "app.main:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]
